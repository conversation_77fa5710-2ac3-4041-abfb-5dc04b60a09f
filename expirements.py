import csv
from datetime import datetime
from typing import Optional, List, Dict

import pendulum
from tortoise.expressions import Q, Subquery
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityDevice,
    ParticipantActivityEnum,
)
import pandas as pd
from ciba_participant.common.db import init_db, close_db
from ciba_participant.participant.models import SoleraParticipant
from ciba_participant.rpm_api.api import get_carrier_list


async def main():
    await init_db()

    # cohorts = await CohortRepository.get_paginated_cohorts(
    #     page=1,
    #     per_page=10,
    #     include={
    #         Include.program_modules,
    #         Include.participants,
    #         Include.program,
    #         Include.created_by,
    #     },
    #     filters=FilterInput(
    #         cohort_status=CohortStatusFilter.active,
    #     ),
    # )
    #
    # total_pages = cohorts.total_pages
    #
    # print("=" * 80)
    # print("COHORTS DEBUG OUTPUT")
    # print("=" * 80)
    # print(f"Total Pages: {total_pages}")
    # print()
    #
    # for i, cohort in enumerate(cohorts.cohorts, 1):
    #     print(f"[{i}] COHORT: {cohort.name}")
    #     print("-" * 60)
    #     print(f"  ID: {cohort.id}")
    #     print(f"  Status: {cohort.status.value}")
    #     print(f"  Created: {cohort.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Updated: {cohort.updated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Start Date: {cohort.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  End Date: {cohort.end_date.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Participant Limit: {cohort.limit}")
    #     print(f"  Current Participants: {len(cohort.participants)}")
    #     print()
    #
    #     print("=" * 80)
    #     print()

    # async def get_filtered_participant_activities(
    #     start_date,
    #     end_date,
    #     activity_type: Optional[str] = None,
    #     participant_id: Optional[str] = None,
    # ) -> List[ParticipantActivity]:
    #     """
    #     Efficiently filter participant activities with per-participant weight device logic:
    #     - For each participant, exclude manual weight entries only if that participant
    #       has device-based weight entries in the same time period
    #     - Uses a single optimized database query with subquery
    #     """
    #
    #     base_filters = {
    #         "created_at__gte": start_date,
    #         "created_at__lte": end_date,
    #     }
    #     if activity_type:
    #         base_filters["activity_type"] = activity_type
    #     if participant_id:
    #         base_filters["participant_id"] = participant_id
    #
    #     # Create a subquery to find participants who have ANY device-based weight entries (ever)
    #     device_filters = {
    #         "activity_type": "weight_type",
    #         "activity_device__in": [
    #             ParticipantActivityDevice.WITHINGS.value,
    #             ParticipantActivityDevice.TRANSTEK.value,
    #         ],
    #     }
    #     # Only filter by participant_id if specified in the main query
    #     if participant_id:
    #         device_filters["participant_id"] = participant_id
    #
    #     participants_with_device_weights = ParticipantActivity.filter(
    #         **device_filters
    #     ).values_list("participant_id", flat=True)
    #
    #     # Main query with exclusion logic:
    #     # Exclude records where:
    #     # - activity_type is weight_type AND
    #     # - activity_device is manual_input AND
    #     # - section_id is not null (indicating it's a section entry and not enrollment weight) AND
    #     # - participant_id is in the subquery of participants with device weights
    #     exclude_condition = Q(
    #         activity_type="weight_type",
    #         activity_device=ParticipantActivityDevice.MANUAL_INPUT.value,
    #         section_id__isnull=False,
    #         participant_id__in=Subquery(participants_with_device_weights),
    #     )
    #
    #     participant_activities = (
    #         ParticipantActivity.filter(**base_filters)
    #         .exclude(exclude_condition)
    #         .select_related("participant")
    #     )
    #
    #     participant_activities_list = await participant_activities.all()
    #     return participant_activities_list
    #
    # activities = await get_filtered_participant_activities(
    #     start_date=pendulum.parse("2025-08-05"),
    #     end_date=pendulum.parse("2025-09-06"),
    # )
    #
    # print(
    #     [
    #         f"{activity.participant.email} - {activity.created_at} - {activity.activity_type} - {activity.activity_device}"
    #         for activity in activities
    #     ]
    # )

    def check_milestone_4_completion(csv_file_path, user_ids_list):
        """
        Check how many users from the provided list completed Milestone 4

        Args:
            csv_file_path (str): Path to the CSV file
            user_ids_list (list): List of user IDs to check

        Returns:
            dict: Results containing completion statistics
        """
        try:
            # Read the CSV file
            df = pd.read_csv(csv_file_path)

            # Clean column names (remove any whitespace)
            df.columns = df.columns.str.strip()

            # Filter for Milestone 4 records
            milestone_4_df = df[df["milestone_name"] == "Milestone 4"]

            # Get unique user IDs who completed Milestone 4
            completed_user_ids = set(milestone_4_df["user_id"].unique())

            # Check which users from our list completed Milestone 4
            completed_users = []
            not_completed_users = []

            for user_id in user_ids_list:
                if user_id in completed_user_ids:
                    # Get the milestone details for this user
                    user_milestone = milestone_4_df[
                        milestone_4_df["user_id"] == user_id
                    ].iloc[0]
                    completed_users.append(
                        {
                            "user_id": user_id,
                            "program_id": user_milestone["program_id"],
                            "milestone_date": user_milestone["milestone_date"],
                            "milestone_fulfilled": user_milestone[
                                "milestone_fulfilled"
                            ],
                        }
                    )
                else:
                    not_completed_users.append(user_id)

            # Calculate statistics
            total_users = len(user_ids_list)
            completed_count = len(completed_users)
            not_completed_count = len(not_completed_users)
            completion_rate = (
                (completed_count / total_users * 100) if total_users > 0 else 0
            )

            return {
                "total_users": total_users,
                "completed_count": completed_count,
                "not_completed_count": not_completed_count,
                "completion_rate": completion_rate,
                "completed_users": completed_users,
                "not_completed_users": not_completed_users,
            }

        except FileNotFoundError:
            print(f"Error: CSV file '{csv_file_path}' not found.")
            return None
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return None

    async def calculate_weight_loss(
        user_ids: List[str], activity_type: str = "weight_type"
    ) -> Dict[str, Dict]:
        """
        Calculate weight loss for participants based on their weight activities.

        Args:
            user_ids: List of user solera keys
            activity_type: Type of activity to filter for weight data

        Returns:
            Dictionary with participant data including weight loss calculations
        """
        if not user_ids:
            return {}

        # Get participants
        participants = await SoleraParticipant.filter(solera_key__in=user_ids).all()

        if not participants:
            return {}

        participant_ids = [p.participant_id for p in participants]

        # Get weight activities ordered by date
        activities = (
            await ParticipantActivity.filter(
                participant_id__in=participant_ids, activity_type=activity_type
            )
            .order_by("created_at")
            .all()
        )

        # Build participant lookup
        participant_lookup = {p.participant_id: p for p in participants}

        # Process weight data
        user_weight_data = {}

        for activity in activities:
            participant_id = activity.participant_id

            try:
                weight_value = float(activity.value)

                # Skip invalid weights (negative or unrealistic values)
                if weight_value <= 0 or weight_value > 1000:  # Adjust max as needed
                    continue

            except (ValueError, TypeError):
                continue  # Skip invalid weight values

            if participant_id not in user_weight_data:
                user_weight_data[participant_id] = {
                    "weights": [weight_value],
                    "dates": [activity.created_at],
                    "valid_weights": [weight_value],  # Track only validated weights
                    "valid_dates": [activity.created_at],
                }
            else:
                # Get the last valid weight for comparison
                last_valid_weight = user_weight_data[participant_id]["valid_weights"][
                    -1
                ]

                # Calculate percentage change from last valid weight
                percent_change = (
                    abs((weight_value - last_valid_weight) / last_valid_weight) * 100
                )

                # Only accept weights within realistic change range (10-20% threshold)
                # You can adjust these thresholds as needed
                MAX_PERCENT_CHANGE = 20  # Maximum realistic weight change percentage

                if percent_change <= MAX_PERCENT_CHANGE:
                    # Weight change is realistic, add to valid weights
                    user_weight_data[participant_id]["valid_weights"].append(
                        weight_value
                    )
                    user_weight_data[participant_id]["valid_dates"].append(
                        activity.created_at
                    )
                # If weight change is unrealistic, we skip this entry but still track it

                # Always track all weights for reference
                user_weight_data[participant_id]["weights"].append(weight_value)
                user_weight_data[participant_id]["dates"].append(activity.created_at)

        # Calculate weight loss metrics and add participant info
        result = {}

        for participant in participants:
            participant_id = participant.participant_id

            if participant_id in user_weight_data:
                data = user_weight_data[participant_id]
                valid_weights = data["valid_weights"]
                valid_dates = data["valid_dates"]

                if len(valid_weights) >= 1:
                    # Use only validated weights for calculations
                    initial_weight = valid_weights[0]
                    latest_weight = valid_weights[-1]
                    initial_date = valid_dates[0]
                    latest_date = valid_dates[-1]

                    # Calculate weight loss
                    weight_loss = initial_weight - latest_weight

                    # Calculate weight loss percentage
                    weight_loss_percent = 0.0
                    if initial_weight > 0:
                        weight_loss_percent = (weight_loss / initial_weight) * 100

                    result[participant_id] = {
                        "solera_key": participant.solera_key,
                        "program": participant.solera_program_id,
                        "initial_weight": initial_weight,
                        "latest_weight": latest_weight,
                        "weight_loss": weight_loss,
                        "weight_loss_percent": round(weight_loss_percent, 2),
                        "initial_date": initial_date,
                        "latest_date": latest_date,
                        "weight_entries": len(valid_weights),
                        "total_entries": len(
                            data["weights"]
                        ),  # Include total entries for reference
                        "rejected_entries": len(data["weights"])
                        - len(valid_weights),  # Track rejected entries
                    }
                else:
                    # No valid weight data after filtering
                    result[participant_id] = {
                        "solera_key": participant.solera_key,
                        "program": participant.solera_program_id,
                        "initial_weight": None,
                        "latest_weight": None,
                        "weight_loss": None,
                        "weight_loss_percent": None,
                        "initial_date": None,
                        "latest_date": None,
                        "weight_entries": 0,
                        "total_entries": len(data["weights"]),
                        "rejected_entries": len(data["weights"]),
                        "has_weight_data": False,
                    }
            else:
                # Include participants without weight data
                result[participant_id] = {
                    "solera_key": participant.solera_key,
                    "program": participant.solera_program_id,
                    "initial_weight": None,
                    "latest_weight": None,
                    "weight_loss": None,
                    "weight_loss_percent": None,
                    "initial_date": None,
                    "latest_date": None,
                    "weight_entries": 0,
                    "total_entries": 0,
                    "rejected_entries": 0,
                    "has_weight_data": False,
                }

        return result




    csv_path = "/Users/<USER>/projects/scripts/solera_milestones_optimized.csv"
    user_ids = """
    da1cbb73ee66429f8dc0f773
a858d7578a9e4b52a5e8f7d7
265672d7146a45a2a2467f56
e44470b1ba6a400da7079790
9f0bd0816a8d4ca8832d0a35
75be685ef6bb46188cd9151f
12fa9ab9523346d3be338883
db4c072454394157998949d5
8ce037fdaa8a4bfcac6f1b69
c5a3898d0d434e6abb7febd6
3511d48ccd4c4cf4889e8ecf
f70c5840d6a244d2a0b067aa
0af580f69e9540099a800c8f
ee414bb51e194d1aaabea4bc
144d4f4c4bda4066a212fd93
9913579e78fb4d42b9223a36
7a076cc13b7b4762baf8f589
2808e83424bd4a5cb634fe3e
3208877c49634556b237a668
6b99a47bcc9643c2a405542a
3812975c110c4612b20a669f
92a36a5f710147a5a95bc5ff
7d12a457726b4bef8cae8b3b
db909344c1664af4b0f54352
59298ff1b16a43afa52df015
f91420222fec4c62b1b593cc
0b7a19ab7bf34f54b2e64f9e
e28e1b14f13944f0a380669d
0be8634d2535410cab7b754f
d691ccf1fd0f46948519cbf8
5cfbc4ef958d40c4be246654
13c66949381a4f6bbccd6608
f586e4c01153494d8753ce98
7528b04049e24b5b900d3d27
d5da21e7a983430c82ac05ea
2bf5446da4cb428e8c63de86
da5425c726e44bbaa88afb00
bd36bf7568e042acbb9ee63d
98a2e48e98cb4dc28b237e12
1019fbb067c9414fbcd8f723
e47e9436bc564f95b0e7fab0
124195c6436f4db2adade203
d512df52f65b4c81972d1c0a
81117128a88f4eb691d041f9
f726e9ee8335498192e6f245
7ca03634f8f34e338d986e5e
5b4954da397849fe9c36a500
d08152ee3e4743af8081eb11
5475e6b537e3418a8f3391db
f02b6c32bd4d4044aed6a4e0
b1b4f0b75daa4359b08cf6bd
a62662addfda4eabbb8ba5f2
79ce34cc93284648b6dbb120
5d0551d835564483afe39811
40aba66c1ded448a9ab16f9b
29d9fae9e3b24de4b9b29904
744c3326c963402e9d55095f
f2ec120f60094c5c84447dac
242cf16693ad42c184d93893
8c7c5847103b4f9982bf0c2f
6d5b0f3e76b74e2a94c9555d
acc28627f43442708c76c716
c611296d67884212a6883367
04884adaa2d547718b96f5e5
dcf0e6349c0a4c188e30693e
eb0358788bb34d799388d8dc
1c0fa60a11fb42c2877f703c
20196047be8f4815ade0e179
e0485994c2eb499abe2bb216
607fe709b6194471b746de29
b62932f0c6a647499e7ac9ff
30fbdff63a1d49d0ac4e4fa8
90d5c3a072984da1bef145af
6107308a27844a759f150079
9199fc52cc8740a1a0b98b76
b75766469397445da561d687
545e145c0ab84050853428b8
0e94ad152b7645a9b7e3a512
d9a2b0bdff7d4c9287fb64b7
4d22c9a678654fec83c418d1
78cccc2bbd9d4ecc91f3546f
b085f5e80ef240af847fe53c
42cd68048a444d0f862a85d8
83de3273859a40e48cd3efc9
a78e765819fb47ce972fe0b9
1c37995d100641268b0c4764
c3c7f9ee52e3484385eb1c28
1a4513646d994e9d857f1154
5ede1ff5304b4a5986b4f593
90739294d5ba403984e37f58
472c56829ee14667aaf2b8a1
6a143618b95147f1a5ba2237
24b7974342ff4db1afd78ab6
d77cac8f270b490f9111d6aa
15cf028ad19547fab6a9ade3
c8007e2992224393a049a5ae
578bea5090e9464a89161024
2c670767f86946c1a57a16d5
55568ff0983e4122a29eba75
54438db6a3a74bf0927d707d
8142fa3ff5e24bce81ba4ce2
715fdd4a5a754456b235c346
6760163215fb4ac38d7a461f
79600ae0d6eb49ac98c10265
35b413b3494d4f71a917c824
70c12b43092e46d5807037e7
bd2fe91ae78244af92432c29
7eb0a2ddd65946f2baaa1d4b
f7c28909fd764c8e98fbdfbd
8b55c2e47695449d98ccf3df
7a54f36a91d9420aba64fdab
3bb7d9163f4f453380ae7d37
8e9d45452dbc40b2b93bfdaf
2f74320610e645d88fad54be
5a65635aa70a40e8932371ac
11a00cea52c445eea43edc5c
2168f03de5814ae2988c675c
b6301c15d4f74a0aa8603674
3771520e811a4ad697578ff1
ee00e3b24ae24de5bbe8ccd0
abfd48077454479e9807bb3c
b5272980ff474bd0bfc43532
33150f098f3c4934aefebcd0
2ce3b358d1d54cd78cd1a3e0
5d9421990dbc420a8147210c
2adf7bcc1f004e45ad3c317c
bab30e6ff09c4c6086148dc6
b4f66bdda36d46169d216d36
df23580be9d84f4da95c251d
ee712ac18d9c4758b18b906c
8f5926b6491d4d28b29b1ece
6470b9870ca74d58a415c731
8153e335b56e4261b438e4e4
62d79402cc16460ebab5e383
3af374e5cb27475c9e659a66
9c675dad66c24cb3904bca0b
f00ece09206140848783bf8d
17498e4525354cbab29370a1
f15316cc81bf4c57ad1abeac
9d1cf898386b4121a3406ff7
01991e21de6b45278b8300e4
8f04874175ff4d93863bb3a5
e441f3da173a4cb98772a1c2
6748894571834dd8b21061b9
bb9f79a8553640389ef6bcaa
10aa69bfd8054da9a00e55ce
3e60f8fefe0c4378951c8ba2
b3c9096bd1f0439b884101be
5d3ed4e4ff3b4eb2b1f16d8d
58f56c61613c419aa2fd2c3b
eeb56c97ed8f4a0cad16a643
10d652f6d1a34d618ac43007
aa96fa6e3b364090acd455cb
7f77622b42bf456db6553760
95db940bbca24dd39ea1ac2c
796305e561d645c18e235c95
6ddc8dd3d7084ef186146aad
f6c3e54560954cbdb93836d5
e908e9b5e17d4002a1c40cb5
6e73af4d24634aa7aa2f7a89
37349a7ed3a04c28a664f148
6675b63558ad4200864b4783
13a829a794b44e2d83c41340
b61a53a2f108418d93f3f1c2
b20c38cb891d4096a20209fd
70b4961036474e8f9cb4c198
9de90151541d4f4495f52d81
1251fd94354d45aabea9ce41
8e8ba5d5ade445868e3f0411
4671e38bf66d468d819b2905
31b7ca06e6754e3d88424edc
132c3f14d6ab4b6cbcccca3a
3fb65338211a4ad9b4d00999
a431fc2db9604e599261ce9e
8d250d1f07ef43e69d98559d
0896a0f2b1784e83a4cf1f9a
7bd81e179ac146a398a2b247
9db54dbaebcc4b848cc794ff
d3294a8128074002ae2a8071
2d2f241901e74023b2a1f381
30cd8defeaff4f4c9a6d879b
0cc5bc367ada46baa407b260
6b5c5ae9bea74582a6bb3bf3
ef4d834d57a24e65a470dfbe
4ebefef8b3804fafacf9a9fc
6eba130eb0954c82a8703dc0
3d241f3c59ad4ee6a64aef12
07ab9bd004594d4e871205a5
0e3cd5b1595c47509db6c1db
4cd06ba0d37e4bb59ad35b0d
1dc695d15e274d3b86dd8d05
515b98888f3947379a2e6ada
203368a0ab03417a90b66d8f
b686933e0f7d402eab9373ca
86349f40fcbc488e89edef33
84b41cdbb62b425ebd92c29d
b922f9be27a442d0a5cdf859
5e5dc3b5289a4384ada8cef6
994eb9462e1b4856a0671e25
b863d7ca59bf4b63996ec71c
8284378c4d464933b9b649f4
8882b0f0d0b04f8bba733d01
3c5adb79033444d893c47365
a349d966ef2449b29f624766
620f676127d94059abc1ade6
3dd721c5c98148b2be4045f9
b0c3b0509d3d4127837ce739
f578cbab3ecb46d682331819
eda686e1d6f14e2fa874d85e
b698e3f5a27f4c0fa1ad4abf
7847c84112224da4829fbc32
f2df3f8cbb824b90a83b7225
57645187e8694efcbaf47076
32da9681df4f492396d6ef02
2d37f70bae0649c18c173863
43a3034b6c9b4372a56062c5
b7af73ece70f46d3baa231da
6a3008bf08ac46a3ac4d8b68
61cde0fcc7bc420b89a121fd
62927ebefe6b4b1aa47c2947
f29e8886cbbc44d890eb1136
eb637b57093b4ff7b4117961
048486eb8cd742e58b6d25f8
55e338afaab5412bb9e56479
747914e1ecbe479685b44c7c
b37a2afabfb7412bb506f677
f9df06fbfb0f4c568ff474c4
04d9a596f0df40d89871a7c9
3c7e6f0c0af84042bd785fc7
1d76ad906a694064a3247b0a
3f7df9d8d7f54eb48aea71af
8d8db1405c514312b7a7500d
c59a0336130f440384e8b108
15e8813240c744db8d6ddc5d
e1d98831cac34aa796c58a11
7a4ab51e4ffe4c6f959132c5
c1fa5044792a4d1fb051bc15
8d1d92cacb77412ba8028652
105219a6b4eb4966a3f3c052
495e7c83e67448ec946bc04e
759bd8989cb143ad986538c0
1813d63ab2ef4f9f966da6c5
2abe65c180fc40f0a69a3192
e0c0de5bb86544c3be0871db
5ae105e2743042b3878606e2
e50ad2d0f10a449daa132912
f18cb55628124b30abfe1b55
dfe68c9a98d64799b0150c28
d0a75d1697564a88b2bf4f3b
612b2447c75f424580d5287d
346192bc06e2477dba0e3eae
defb975d8d694a0aaec38758
32fbd3c6f53744e5bd15a884
7238586a5b4b43c1b14a38cf
b873c618e4834ac4bbdbf565
f95fe80e791d47d6b52129eb
cf13cb163c074f3e87e52534
3c9758dda5af4208afa42c57
1716980a49aa445a89f06b8a
87c1094df11647fa92f772f8
b5db5406b69c41cbb4a8be0c
1f7bc27f64a94f159db71302
7d04d12c87274e8c94c46373
99b285f6b30c466bbe1facee
9caedc8e076c42f088eec27a
95f3434112764c75b5b391bf
1b46de9574534d11941f238b
0daa47ba98a04d2a9c571060
04580835a8c74457b0b822a0
70d6ac556e8c4b718ff534ff
c10187848dbe4cb898ce39b6
b5bf6e2220a548df9434c74d
45498741b6d9401da64b3904
eff3cb878ab540bf89cbf3ce
8f6cb30fa4084dd1ba2a7317
1ab78155842543e7bda4e6b0
9c64610cd20e41d9b209e639
56c189655e3e47cf8ecbb5bd
a6a84a7a034a475c9ea3c4e6
11a2d8a7db2c44e0b0210354
8e4b9fadb0e94509a98d8995
430d9e09a45843308ed9e8ff
16ece82175564b2385dfe095
92a2d44bb6834d819aa1d49c
d71119060a7b4e85be5f2d6e
819cc9f10363458dad6fed49
83f9d84ed83a4a23bb4b80d3
13ef3d4ff0d5475a80a5daff
b0eb89f8638a4195a43235fa
c82d56830f8042c4aca0ca68
43c67a967dda4f29b30558db
11074d350c9040c198579da7
59b10f5f28684d41b271d895
0b88825efe254dddb00a8b88
92e1e13f3c474426b73ca74d
d9306ddfbbaa4ed390cc8941
8c373b03ad314332ab6b3a73
81947b2d0ce14a4b903d096e
54286ce64cf14347be479826
79784327ef08488a80247bff
d8a4bc81b6dd4cd3a157a846
1543fe74287a47c88bffc370
04219ab6a7a740d2912c75d5
085a9df7a54545c0b2ea9b68
0b78519370cc446ab9497509
0d6bc7bdc8564a8fab31d4b6
0e511a43c3554dd0bdb4081a
0eadac0c48f3410680adb27a
12043aa7dbc0418c902b6855
2567ff58508d481c92bf1d22
2c5b693cdc57432fbf053eb0
2ee95122f7814e0bb197cedd
3393d3ae82fd4df994378797
37d6e4500fbe4e60b0321373
49bb6815c6124e318156409a
4dd02ec312d6444b9810812d
5adcf0f03f5f4568a4a4e2fa
5ca92bf2330342c0bc7c8be1
5f5a07ea9f704e9198cf63c2
64237f52baba4a1e9652fcb1
9866557256044107b458e524
b8e5cce53e614bdcb297a745
d68a3a233db94dfeb2a85be4
d8cd9f9567134791a06e4dbd
e311b034659743308c39ecae
5294717e84894d1faa6f457a
f0727d013e814bb4938ef4c6
f25927b403d24254870b5f99
a3de48843d0943ceab43c4fe
14605c2822ee436f99fe25bc
1f7fc7f46d50417fa5640c9e
39aa511528af47848887bcfd
4e4692ae298c4eea87e68dc3
52475181f2ef476ba48df395
5281c9c6cbf941529f66e45b
6a9f95f5eba840d2aa0ae76a
6efd068da74f4c96af08be01
72c732ca8324473e983c21fa
779d2bc0a7874c9eb25a8d4e
96304dac6ccd4ba1b36059f9
96a51c764191437e9ab5d192
9a1f00812d354cea899d12b2
a836cb7404c64566b934366d
b1cf6d88a2d1456289f1b516
b657358aa993418e8f08480a
ce05a498b53a4359898326b6
d970bb1b6280447687ad8155
d9df185aa8174622a5a910ef
e4eada4adc694b658c98ca49
0b3e10ccbf324cd988f1a43e
6d24ca5d2c404bc68e7547ce
6866f93129d744e3994d0b54
18f26921dbd44ed59779a23b
eaafe9c8c58b4f6f91419851
aee288058a77447ca3a109f2
7d3a868393084b2f9ae60b3a
8439711cc3e643adb4832c74
1d13c3acf5d34a5a99115234
b60ff0b3e2d9448294f6ab7e
d4e5203eb65c484c8009f49e
a9bee3dadd264c7596c42621
92e6e3fc085c4511ae203d2e
42aea0e5db504be290cccfe9
a019ae8370f247b59c593d70
bc5507467cbf4a7a88b13f2c
3b55cac331f547bd9ffccad7
3071af5843e449a7bb4ef712
e39137f8ff8144ecbf44019f
139ed386e2db4323abe54d02
fa77b55a8d684ca6a7ce2209
61ee126b06a645519f39770e
bbca8912590044418faa3b89
5037bda3400742f8a8b17044
4a8fba4e9c47433fa1e3740a
e3e60c0bec584523a5887817
3133af393fc048cb94af63d2
338f9b405c4e4fce89f4e8f4
4650b37fa6954cfdb3a51e84
d23042286bb141089c4aa4be
ba1f1482d49c4b6f9e40a62f
909ab173f8ce43518a420ce2
3957c7f407c24566ad90cd9e
61f4eb27b53d45a8b4b67f49
41919a0597a4456d90ef9f25
b170835411bb468abbc38e44
e36083853e6f40e7a99c1e6f
3d2f6d9b47534dfc87408999
2768b5fdd10f47e58a37df57
e4a313e403954cc4a895c599
86d7ff59c83c4450921b5abf
7be7e337bec843ff89c1b7ac
2477a8e5667346599c49e877
cac19524b9264c18a8d60c2c
e179d2518b5546049650f494
40c6a1d79a344e20865ccf75
634f4ffbb83b4cdab747c013
afda916ac0af4bc08f998a13
0e009dc62721470fbd1005bd
32060da23c84407290c7c073
c4a3ff2c2a424595a8192dca
9b8676e73c034ae9b0e158a6
088494f501804b3eaa9198d1
7acc29d34af84fa88cbeec59
e5d1cc6c4b804e6aad6a2363
f4247114e4eb4a0b8f746d90
c875ccb823f341e39f552760
52f224ca3f2d41479ccac686
9d8cc697c9e24e4cb98a7794
d59c9bf4a6dc4a06b34013e5
2b6510bda52344058856b0a1
f6782d4e9e4a41ae913a3874
30069320de6349abbfbaadca
d6c1424f3851458981923acc
089f1cc382e2496bab8c03c1
952557e1fe4946268733a090
0ef5830661934fdda40b0a87
6287ace39c2e469d8089a4ee
7fa9dbec176949c194975e9f
4a51aa7f636149348666671a
b045afc6d4a544198d778e51
77d07ba28df74f079427d327
cfdbf9348c6f426d904b2303
4c03174fcf9f496a83653c94
01e23d02ea4b44dd899da048
2625f425b01745429e0e8e2a
e9914db018634aab8ff3f7d2
8e791677de0846388170ed67
f26fd44d35a14042a55c87f9
0310f61820eb4454b9428d8e
4a4e25ff223e46a4a4022e2e
3121f399e0f84f10b19309db
e077f05593074d2487dcd002
f72afc4aa81a44a4a457de05
1b867a36d6eb463585fd48c8
50200c7cff444350a6b55e28
7bb364d7899b4aafa0aceeb3
e8cf19b990634f4a8a157126
11dac4f9eb4347799d019945
01d5f2efbfd54840b10ac9d9
34bef989a9eb40c09671cb89
f90728057d244a63b4e01574
765c43ba92db4bbc97eebad5
7290680836064f9cb59fcb58
843bd59b07e54fc1b62fe0ee
a1d84863cd344427a4b40ffe
89d42f3152f145d3b74daf47
719aec91bbe440fdb1304d4f
bdf79f55bc2e482c938d2ada
8c3141e1412e4cdfb600c68e
bde40c2de5c9446f8937ca00
5c993be6af0c4760b0117c81
727e338197054a8abc0fe48d
ea44aa0debce48538f0ba576
8f634d5d8b984d2c9bacb8e3
351a46193f634be5a4642c01
3c9a67562b4f4ee9aee2989c
10f79125a65b418f9d81d7a1
a67d6123939e4c619eef3b41
03fd9e8671d345e1b6199caa
98b9da7582e44a188a0f276d
bf61683ef6004787addf8af6
ef06f35e3e4c4a3ab768f875
8bf2f23719464dae995d9bbe
94f9a27d48f74a9d98ae6a76
560b24bba35a4c8dbdb9f72f
a2750bcf4b9c45438005ec54
af4985cd764642fe85158ad5
eec4cc583f8b4313ba52a445
a56428757921444f919db439
89f0f877f7b54a4d80159e24
459913b0d8494330ac3b52fe
d7b7f75c80674dfba0c1e863
2ab681f2e67d421487717f1d
b0931bcf2c9f4ee6b1b5d0d1
f328d7bbea53458a9a466c12
ac8d9d80a092435a8ec643eb
28e7fe4f796945119bf255ae
8806304c515d4fcda104f2ee
f9f871d1a8b74de99366b8bf
c922d46ec24e40b1a8b844db
b4e106e3f1d448dd9195aea3
5e6fdde1a14b46fd8be765f2
e7f45063db704f9e93da8fed
3c88723af774468d80f61fa8
164e82cde15d4182811159b7
e6a5bdc5395343be91324c28
247b2c09058541169709c4fc
d5d52f68bfe145c9819f161b
58bb5dccad3449ac9fa55843
e406cff373bf457eac955b17
50d4511c2ad24c96a021ba6b
97035965eef04e1d8be839eb
8b2dbcf298fd44b3ac019505
0a50424d41ab4d71a289f382
be0d0c094cb54774a518c06e
0bb50956e5454f5c8170ce88
bd89305173b94fb3bdfff845
733ef7db45f048949c857134
80e50768782a493889bd150a
d0e5071a08cb4445b659494f
846e9853e4444f068df5ed22
770f280fe12b456787e4c393
64cef8bcaf23445cb2fd70c2
11927784a62e4c25bfad689c
1cb768fa51064f7cb149bc89
03f264c0a98940379448a814
f1c5614a0db441af93417ae7
370faa25cd3049cca46d4f57
17db2ae4f74e40dea64fa804
a8fcefdb51ee4a1cb4188d66
f46662938ca9459fa6a1ea0f
1d74ac559f0949889f066375
7adae458e56741359102a9aa
e3e474dfd6e044c2a7341364
f42c34f77d634265be7a1a31
9baf7b52a24e46f3a2e46697
9f24ccf0759548e09893db65
8f457a3588954ea4b9f96b63
40cedfebff394f25930be100
7ad22b1300c6432981cc139d
bfef9235b48443b9be48c457
75eabae0447e4705bf158113
ca2119ba183c44c0a88a2483
5d77dd8b2eaf41848924e982
36b9179656264fd1a83c2a07
c66cb626ea88488090760be1
30d92b6539814c0390b2b33d
f77ce1fadc2544d18b14d57c
02866b063de746ff8829706d
7d79d3db039b43e59bf37897
f671662f2ca44ed8b165b522
ea30671bd378429fb57d1933
5150837937dd47af83b82542
3e0cab34caa04d22a2027a55
3a24d9d0ebfd41f0b6d77346
ed7373aa80a34d3c8c77bb1f
96183c7233404c76b5a72f96
d9eb1e252d3142a5917f6b7c
afce831ef74d4bf9876bc810
c66bd5b3b9424eb585697307
b37a35a51e9c4d55be2a3af3
3af50b86a4e44e8d99846aba
9664721e797d4951a19cf5d8
c929750c6a134ab092f724ef
39af485a22364acfae2cf878
27fe4ca28626466598e97320
a9d2c3c0012149e7ac9dab6a
765ddea77bdc49cea41d924e
2423cd8de96d49879bc8667d
b5386170264c401f99c5d309
67bd4184f262443eac2390fe
fe69eb85710f42ceb9e1c949
e03002a8c705405bb8d087ad
28809b499e8244009516cab7
8ccfd1ad35e44fdc806969bb
080a9128a87f4044adf7e09f
49a41950deb947b3a3327b75
9fb1ee5fe68e4211a4004466
e70fa184cde74a14882b48d4
9c92a3829eac4a718819d1bd
433e115538ec42a283794647
9b5400108cde49d19fe42d04
6dcd1c85c061474c89333046
33a2ae953ef94eea86dff500
8ed39bd3297f4551b4533121
a98626bee72b49c1a1003872
a978b089499c478fab11798b
a7ce87f2a17b4bca83a40c11
e8a32589540e4f948375bf95
ebf25ae03be74a39bed9cf52
202c64deefa245799144de1b
4e24418716f843748214c715
b9842ed1e6e543398fa24611
46e1ae91b72642c7a24d4078
aad57b65864244e3855348f5
020817dc00e84d488593d07b
c797235df9eb4c16ad1ea77e
56878eac90c04ede8ec68608
8d94c199498a47a3a7182723
5a9daa08b5084651839a2ba3
01df5f68e74542888536c94f
b1f47b2f34944fa3aa40f599
a63332485e81436b8214fd50
d98e2057ee294384bb345297
fee956eb2a6841379a6305e0
bd49e1edd83a4bb1a10113bc
962d1962111e47498a2486ea
a11181f08e7d4826903a22c2
a44e4b54965c49eda183ce0e
65bf399c17274ee1980b413a
7e5e96338bf543aaaed784f1
6e31a125df62479a90b10bb5
16212594d2b448858846df96
92685f84d6b34c92b26d2614
061bad07c9ae40f6be7f4729
c409cd640e674dc5bdbbeb5e
31d6788d0d5a4bec92a57197
9a538448852b43aaa4989c26
d90f0caef78c4099b0e9d058
f9aad8c2195c41c2a00d77f7
47a731e9c27e49bba48ef35d
dea9b6d431d24a879daca7b3
86086589c6c84a2d808dd49a
dc23633682dd4e4992d91793
f47e50de727d496bac420331
dc36ff9dc78e4b06a5996f9f
1a923261cd1347ff83670358
2511d0ca851b4987880ac6c0
2727353432034c3887a172c2
6bd29cb0209e4230bf82b3a8
660fcf0a775444e991a6bc66
5b0380f5ce324f74be7be59a
2df7d641f46a4aaf85ae0ef5
d66e7049eb7c4a798a691823
a8ca596b386c4537b6cd427e
94eea599843b45b1b1306003
70be5ee1fd5d4fcf95a95b17
5ff4f7a06c474a2d880cb377
4570b908a31444458574efcb
3a5fd3aa50304990a635bc05
d10e9c98518141c9a0374b1f
02bd519df3e2405cbc66fe13
79dbf6e6c1ac44558a9a0ae0
bef0bac08e5e4828bb2917fa
a1a80a9aca9f45888b5b8f4b
4684402a0bd34e6c9a5640a2
238ef11917b945cd891e944f
7efaff97cdb944e696b8e72f
1c0cc8b72c1a49e988a94700
e13697ed5dd441a5841a2fc5
59d5b820e50f4d4989de24e2
da2254fd43254b419e81f15f
6a4bd3c9b91242a8a5704c0b
fd9a6544022c4c5aa1f90571
20b8b6cd5d734684a727b35e
031274718c994c038c6056d3
2173474cd6f94d96b12dc491
8727d4ea267e4b7c8e96625a
aa18d241dca0415fba98565a
dfd02ab70f0f4097b38d9790
8c30fa9221c647ce9ab88a02
c1f43bd1c4034920a5c23ea3
8558ea7d592349dcaf671a86
4ef9b1936c6d481eb322272a
23f562c45cec45f79ba8ebf3
020eeb04deb842b7adc2c46e
5e1139d10a4847edb222f1cf
793d6bcb2bb646a887b2b7fb
b7492eed619b47008c541298
771b603827a1427db809ca56
d6e9b50f43d8428ea0344edc
46f261fa0631434682737758
caf097a3434746b1921eedb0
db49ded6f19f4550bc49c60a
0abff7f944b747a499a7a73e
49e932f17a27461baded423a
946b2aa826b740a1bfce1607
c903d3723ec548238c7432d4
32645ab932d548c7bc58505f
791a883daedd4bf4ad5f7d92
e317ba83fc1b49a9909bf56b
b5e0f0e6c01047b39bbcfdc0
302c92adfbe04d8eab9a0df6
16592ac46c3644a3b55ea4bc
073be64c17f846b888328740
99f9ebf58862422ca9188884
928f42215266498c8be36cf5
f9e3776bf82a4d57b1a88a11
54fdee6e5530464daacc082b
75c87229d04144a493ca250d
ab6ba6f70e784ab6a4bdd23b
4e9099b82afb4844b19eff66
d85c9b3f89fd499699cfe827
84821bf1b2b849bfba739969
dcea543362284f29b644a0b2
01114263e5994e1bbd4409fc
0bb7fd6f46f4413cb16c30ca
b015d6fd22194346a555a3a8
e878d63b2f2340368d458c98
8399fe2db6b84469a3c938dd
5eecfd456d854439bc551fa4
8775facc5e094a8e8063ddd7
c55a95b7478448d2aa223a2b
9ba7a92102df408193c5b51f
60b3d68d478a4eee954d2c43
a5f2ef8d8b8c4c6bb30a560f
375b29268bcf4669894c6d24
80920ffd4fe54dc88a030b4a
06ba523d62fb4503aefa93ad
8a6e1a8b499c42c6a08751ed
a316a27b99804b1d9d4c4b1e
a401edafd2b04f48804d26f8
7ab5057503c34e2aba0ad793
429817ae72494623953cb536
6b4c32abed2a40b58cb9252e
cd864a2cdb6645b380a277d9
5408c7158c9642d58923b704
756940099e424268a5ea4dc7
18fd1ec652e2446d85478e26
41991afa97584205b29802bf
78cefa674b3842288b07820d
4313becdf0f7474389ce82a6
2405bf6c79bb4dc9bd251363
49c441556eb141dc9d2f306d
c866bb7daa9f4627951261ad
e8df08119a77492683387be2
0bb383473cdd4216ae2d2436
0a1928d26f0d4a5587a288af
267f3f3a0f274b1a97d6a5cb
30b20354239d453aa15280c5
22aa2add317c4c3c811752e3
4b4edbe1ea0d4af0a0ba665b
2c42ca6267d94e19b4965e8c
80dcc352bbe1493498ca8f79
c8ac2af21c9c4120b12b7419
12d8ae721213452b95a1052b
d73273d6d73a43f6badfac5f
f26f85765fb64091a50dd0c4
5db7302fd827491dba54ba7f
0061e4b29d6341c99de18693
70bc9701788f4a7193738c06
ea72740855b2401abb7979d7
0099a6df1e2142719e42b128
730e402eacbe411e9a140426
25d6fd752a8649ec9a4b4550
f15abf49c36e482fb38058e3
4126212cd2764128a89ec55b
add50d37ff5c449a909697bd
648959c34f694cd890b1b5de
9c20fc0bf51b46f394638273
befc6b91ed3240b8892551d7
0806c8cba0a84890ba33995e
18a34150d7364c2eae3a0587
35b619d4dfd44d1f94ea949b
5d9b9046f0e8485685888ad8
e50ea07b5a5e42b680fc818d
2879e0869b4a4a328477f036
f4eb6c6b39694e8c84b1d98f
a36283833070428b9cc2db9d
2e065046a04d4e5c8cfd3f14
a4f53464dfc446a98567ab9f
13e2d182975d4577ba4da501
d0476422b20f40cc838df933
6187c718fe0444d482f41318
fea6349dc9e440bcb9cc13d4
f40cafe826ea4067a596c7cb
da4303c6321f4f97afbbdd24
e9f6cfccaa6b4bcb9bf731a1
7ca588774b3a460f907ab1b0
6f2ff4a8820d459f87c64346
6bb0fc13103e4fa5802b7ee1
eaf85f68825b4078b71b06cf
a31ac9ccb28141c2a0dca514
f62488b97b4c4e23a43944e7
1adc680e05f149239abe7a02
004e5326f39e4f1fbbec2a1e
dee7929afb894963969fc567
c07a3039d8db4f19997c8cf3
27f08879d0174e6fa6c82081
3102768c50714dc4ab8a5b2c
46135b0ffa1c468492e3df95
de4c13cf80b84dfb913bcc46
152ff03df82b48b08fd502db
f673f801008546c28f39fe36
cc87e63ea134465ca9bcfeb7
3b661ddfdc67405e992b329e
b1a91c8be2754446848379a1
fef943d663784f1fa822789e
bd8f42e880e047e3b6e912f4
ec775df3af624db7b2791346
d7648fd5c26d44a5b96f9e83
b76ee99e9bcd4e2cadb7f578
5619bac100454d74bb56ff59
a4981007c3834b9d8f11d564
a50e1a678dbf4fac84563c41
c432e4d791ef4890892b768a
c81c256d5a654552b907162e
2f93b7b1a0f94ff395657d4c
abd041f4cc0a4d1e8dcff57d
31d082d1d1634bce80ba3016
4156e8e7e7a64be3a4cfd9ca
12f4805367da4ab1b495b12a
ea23626ecc664e6aa3df24d0
db457c4edc204b4eaf3506e5
df1e6a674da441729ead9d64
c7617681db1a45d2bdb4b10f
8338eafcf63b4b04bf89c3b1
7d3b06449d55463aa4c7884f
cadbfc0331004b3ca2ff24dc
9765d526a5a8474bb253f4d2
ae8d07163760474a904fff44
f6a87b1ff6a8406098f686cf
6153202e8d4b40ba87c55bcb
d3652ba5670440bd9427f6f0
059aec4a8a02475991bf492b
f3978542c1a94439b73d86d2
ced4070f1c994ef482777dba
741faa7626a5418198d038b2
49cb542610834e5c83522884
00821cfb2eed42e5a147244c
8fb890b4a67e4611ba000b65
0670ab5741b94c7d91489d8a
3a2db157cc4444f694fceb71
4be5b1c827324281a1ab9a88
3e7a8150d3da40d9af363ef8
5f33ad6b2c2c4feba9235f57
38a6a8238a85444ea1699baf
612094cc893641189fc3a61b
3e6044d483ef484b9a9315de
9d4d4e5ddbf6490eb193a4df
e77a2ffa9376401899bfdb86
b1c6e0ac3d274600afe1c0c4
8d793814ed464d09a7970b63
ee5bf1d9b9af4e1bbd9f3403
ec1796c420cb4df09885dfcd
53ce23b596f9469b8d46ecf5
d16524573f5742b1a1d8f7dc
e65e091f8a8742c5ab0e6158
0739f700b0494d25a37b59da
cff527b7356448fa92664a4c
c107d6ff6d97497c97cc593e
a74ce302a2404bff9323edd4
1379c04064fd48afa13e21bf
58bfbd5e6e9741e4bdb69a86
cbd1896ff98a4913be1bde7f
bc2e94f00f1140cfbe028d94
704e3c678d3a474ea1ca60f0
0ec65f8f11144fe684cf1b28
d7cf63749bf648e09c6f271b
2397a30f25254c358f664a98
29fe9e40a48b4567ba2f68ec
1775120b6ad9418d95317355
762f58c28275486fa3b6fea8
d9c72476a5e2494e9aaa8f5d
f55445ad3e94415cb3762c87
11a04afc155a4b0a9606c9d6
d88282133fa649dcb70799df
0bc542449fa64915aa95c633
e17fe5c8f7a94c129b0732ca
002811528f2247f3867ceacb
1b1a8805c56a4b5f9a70c172
36e258fe17134603912d4ee5
09a3a5ac43324f67bce82e1f
f49be5f1587849c5b577585c
cf24be1227de41e09767d699
3a91dcd131fb419f9c24910c
f4866f6158ab49a6bb20ea69
9f0d716df3154dd4ba9fff1b
53859326f7ec4be2aa355e19
3dd25a3f6adf46579813a425
2ac0f14705a4453db8745817
bb7ef9c964f642f0909f8b00
2845167b10cf42399d3cbcc2
c3c7dfb6867c4414aeb11a57
"""
    user_ids = [item.strip() for item in user_ids.splitlines() if item.strip()]

    results = check_milestone_4_completion(csv_path, user_ids)

    weight_loss_list = await calculate_weight_loss(user_ids)

    completed_users_ids = [
        user["user_id"] for user in results["completed_users"]
    ]

    print(results, completed_users_ids)


    def create_weight_loss_csv(weight_loss_data, completed_users, output_path=None):
        """
        Create CSV file with weight loss data, excluding users with no initial weight.

        Args:
            weight_loss_data: Dictionary from calculate_weight_loss function
            completed_users: List of solera_keys who completed milestone 4
            output_path: Optional custom path for the CSV file
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"weight_loss_data_{timestamp}.csv"

        # Filter out users with no initial weight and prepare data
        csv_data = []

        for participant_id, data in weight_loss_data.items():
            # Skip users with no initial weight data
            if data["initial_weight"] is None:
                continue

            # Add milestone_4 completion status
            milestone_4_completed = data["solera_key"] in completed_users

            csv_row = {
                "participant_id": participant_id,
                "solera_key": data["solera_key"],
                "program": data["program"],
                "initial_weight": data["initial_weight"],
                "latest_weight": data["latest_weight"],
                "weight_loss": data["weight_loss"],
                "weight_loss_percent": data["weight_loss_percent"],
                "initial_date": data["initial_date"].strftime("%Y-%m-%d")
                if data["initial_date"]
                else None,
                "latest_date": data["latest_date"].strftime("%Y-%m-%d")
                if data["latest_date"]
                else None,
                "weight_entries": data["weight_entries"],
                "milestone_4": milestone_4_completed,
            }
            csv_data.append(csv_row)

        # Sort by weight loss descending (most weight loss first)
        csv_data.sort(
            key=lambda x: x["weight_loss"] if x["weight_loss"] is not None else 0,
            reverse=True,
        )

        # Write to CSV
        if csv_data:
            fieldnames = [
                "participant_id",
                "solera_key",
                "program",
                "initial_weight",
                "latest_weight",
                "weight_loss",
                "weight_loss_percent",
                "initial_date",
                "latest_date",
                "weight_entries",
                "milestone_4",
            ]

            with open(output_path, "w", newline="", encoding="utf-8") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)

            print(f"CSV created successfully: {output_path}")
            print(f"Total participants with weight data: {len(csv_data)}")
            print(
                f"Participants who completed milestone 4: {sum(1 for row in csv_data if row['milestone_4'])}"
            )

            return output_path
        else:
            print("No participants with valid weight data found. CSV not created.")
            return None

    # Create the CSV
    csv_output_path = create_weight_loss_csv(weight_loss_list, completed_users_ids)

    print(csv_output_path)


    await close_db()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
