import math
from datetime import datetime, timezone
from typing import List, Optional
from enum import StrEnum, auto
from uuid import UUID

import pendulum
from loguru import logger
from strawberry.types import Info
from pydantic import BaseModel
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from ciba_participant import get_settings
from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.classes.models import (
    Booking,
    RawLiveSession,
    RawBooking,
    RawWebinar,
)
from ciba_participant.cohort.pydantic_models import CohortInfo
from ciba_participant.common.cognito import (
    admin_create_provider,
    admin_set_provider_password,
)
from ciba_participant.participant.models import (
    Authorized,
    HeadsUpParticipant,
    Participant,
    ParticipantMeta,
    RawAuthorized,
    SoleraParticipant,
    RawParticipant,
    AutorizedRole,
)
from ciba_participant.participant.pydantic_models import (
    AuthorizedCreate,
    AuthorizedPydantic,
    AuthorizedUpdate,
    HeadsUpParticipantCreate,
    HeadsUpParticipantPydantic,
    HeadsUpParticipantsList,
    HeadsUpParticipantUpdate,
    MetaParticipantsList,
    ParticipantCreate,
    ParticipantMetaCreate,
    ParticipantMetaPydantic,
    ParticipantMetaUpdate,
    ParticipantPydantic,
    ParticipantsList,
    ParticipantStatus,
    ParticipantUpdate,
    SoleraParticipantCreate,
    SoleraParticipantPydantic,
    SoleraParticipantsList,
    SoleraParticipantUpdate,
    ParticipantClassProgress,
    ParticipantAddress,
)
from ciba_participant.program.models import RawProgram, Program
from ciba_participant.cohort.crud import CohortMembersRepository
from ciba_participant.chat_api.chat_api import remove_participant_from_chat
from ciba_participant.participant.service import ParticipantService
from ciba_participant.common.aws_handler import (
    SQSNotification,
    send_to_sqs,
    EmailNotificationEvent,
    NotificationType,
)
from ciba_participant.cohort.models import Cohort, CohortMembershipStatus
from ciba_participant.common.cognito import (
    admin_get_user,
    admin_disable_user,
)
from tortoise.transactions import in_transaction
from tortoise.exceptions import IntegrityError

settings = get_settings()


class DateRange(BaseModel):
    start: datetime
    end: datetime


class FilterInput(BaseModel):
    search: Optional[str] = None
    cohort_id: Optional[UUID] = None
    created_by_id: Optional[UUID] = None
    program_id: Optional[UUID] = None
    date_enrolled_range: Optional[DateRange] = None
    participant_status: Optional[ParticipantStatus] = None


class FieldEnum(StrEnum):
    first_name = auto()
    last_name = auto()
    email = auto()
    cohort_name = auto()
    created_by_name = auto()
    program_title = auto()
    current_week = auto()


class OrderEnum(StrEnum):
    asc = auto()
    desc = auto()


class SortInput(BaseModel):
    field: Optional[FieldEnum] = None
    order: Optional[OrderEnum] = None


class ParticipantInfo(RawParticipant):
    phone: Optional[str] = None
    last_activity_date: Optional[datetime] = None
    date_enrolled: Optional[datetime] = None
    cohort: Optional[CohortInfo] = None
    dissenrolled_reason: Optional[str] = None
    disenrollment_date: Optional[datetime] = None


class GetParticipantsOutput(BaseModel):
    participants: list[ParticipantInfo]
    total_pages: int


SORT_KEY_MAPPING = {
    FieldEnum.first_name: "first_name",
    FieldEnum.last_name: "last_name",
    FieldEnum.email: "email",
    FieldEnum.cohort_name: "cohorts__cohort__name",
    FieldEnum.current_week: "cohorts__cohort__started_at",
    FieldEnum.program_title: "cohorts__cohort__program__title",
    FieldEnum.created_by_name: "cohorts__cohort__created_by__first_name",
}


def process_participant_cohort(participant: Participant) -> Optional[CohortInfo]:
    """Process cohort information for a participant.

    This function finds the active cohort for a participant and returns its information.
    If a participant is in multiple cohorts, only the active one is returned.

    Args:
        participant: The participant to process

    Returns:
        CohortInfo object for the active cohort, or None if no active cohort found
    """
    # Check if cohorts relation is loaded
    if not hasattr(participant, "cohorts") or participant.cohorts is None:
        logger.warning(f"Cohorts relation not loaded for participant {participant.id}")
        return None

    # If no cohorts, return None
    if not participant.cohorts:
        return None

    # Find the active cohort membership
    active_cohort_member = None
    for cohort_member in participant.cohorts:
        # Check if the status is active (using string comparison for backward compatibility)
        if cohort_member.status == CohortMembershipStatus.ACTIVE:
            active_cohort_member = cohort_member
            break

    # If no active cohort membership found, return None
    if not active_cohort_member:
        return None

    # Get the cohort from the active membership
    try:
        cohort = active_cohort_member.cohort
    except Exception as e:
        logger.warning(f"Error accessing cohort for participant {participant.id}: {e}")
        return None

    # Process current week information
    try:
        current_week_program_module_raw = [
            module
            for module in cohort.program_modules
            if module.started_at <= datetime.now(timezone.utc) < module.ended_at
        ]

        current_week_title: Optional[str] = None
        current_week_module: Optional[str] = None

        if current_week_program_module_raw:
            current_week_program_module = current_week_program_module_raw[
                0
            ].program_module
            current_week_title = current_week_program_module.short_title
            current_week_module = f"Module {current_week_program_module.order}"

        # Create program and created_by objects
        program = RawProgram.model_validate(cohort.program)
        created_by = RawAuthorized.model_validate(cohort.created_by)

        # Create and return cohort info
        return CohortInfo(
            id=cohort.id,
            name=cohort.name,
            created_at=cohort.created_at,
            updated_at=cohort.updated_at,
            limit=cohort.limit,
            started_at=cohort.started_at,
            program_id=program.id,
            status=cohort.status,
            created_by_id=created_by.id,
            program=program,
            created_by=created_by,
            participants=None,
            program_modules=None,
            current_week=current_week_title,
            current_module=current_week_module,
            total_weeks=len(cohort.program_modules),
        )
    except Exception as e:
        logger.warning(
            f"Error processing cohort information for participant {participant.id}: {e}"
        )
        return None


def process_participant_meta(
    participant: Participant,
) -> tuple[Optional[str], Optional[str], Optional[datetime]]:
    participant_meta = participant.participant_meta
    if not participant_meta:
        logger.warning("Participant {} has no metadata", participant.id)
        return None, None, None
    else:
        phone = participant_meta[0].metadata.get("phone_number", None)
        disenrolled_reason = participant_meta[0].metadata.get("disenrolledReason", None)
        disenrollment_date = participant_meta[0].metadata.get("disenrollmentDate", None)
        if disenrollment_date:
            disenrollment_date = pendulum.parse(disenrollment_date)

        return phone, disenrolled_reason, disenrollment_date


def process_participant_activity(
    participant: Participant,
) -> tuple[Optional[datetime], Optional[datetime]]:
    if not participant.activities:
        return None, None
    else:
        activities_sorted = sorted(
            participant.activities, key=lambda x: x.created_at, reverse=True
        )
        last_activity_date = activities_sorted[0].created_at
        enrolled_activity_model = [
            activity
            for activity in activities_sorted
            if activity.activity_type == ParticipantActivityEnum.ENROLL
        ]
        if not enrolled_activity_model:
            logger.warning(
                "Participant {} has activities, but no enrolled activity",
                participant.id,
            )
            enrolled_activity_date = None
        else:
            enrolled_activity_date = enrolled_activity_model[0].created_at

    return last_activity_date, enrolled_activity_date


def process_filters(query: QuerySet, filters: FilterInput) -> QuerySet:
    if filters.search:
        filters.search = filters.search.strip()
        search_terms = filters.search.split()
        if len(search_terms) > 1:  # Support for full name search
            query = query.filter(
                (
                    Q(first_name__icontains=search_terms[0])
                    & Q(last_name__icontains=search_terms[1])
                )
                | (
                    Q(first_name__icontains=search_terms[1])
                    & Q(last_name__icontains=search_terms[0])
                )
            )

        else:
            query = query.filter(
                Q(first_name__icontains=filters.search)
                | Q(last_name__icontains=filters.search)
                | Q(email__icontains=filters.search)
                | Q(medical_record__icontains=filters.search)
                | Q(solera_participant__solera_key__icontains=filters.search)
            )

    if filters.cohort_id:
        query = query.filter(
            cohorts__cohort__id=filters.cohort_id,
            cohorts__status=CohortMembershipStatus.ACTIVE,
        )
    if filters.created_by_id:
        query = query.filter(
            cohorts__cohort__created_by_id=filters.created_by_id,
            cohorts__status=CohortMembershipStatus.ACTIVE,
        )
    if filters.program_id:
        query = query.filter(
            cohorts__cohort__program_id=filters.program_id,
            cohorts__status=CohortMembershipStatus.ACTIVE,
        )
    if filters.date_enrolled_range:
        # We can also use Participant.created_at as an enroll date
        query = query.filter(
            activities__activity_type=ParticipantActivityEnum.ENROLL,
            activities__created_at__gte=filters.date_enrolled_range.start,
            activities__created_at__lte=filters.date_enrolled_range.end,
        )
    if filters.participant_status:
        query = query.filter(status=filters.participant_status)

    return query


class ParticipantRepository:
    """
    Repository for managing participant data and operations.

    This class provides methods for CRUD operations on participants,
    as well as specialized operations like disabling participants,
    authentication, and program management.
    """

    # -------------------------------------------------------------------------
    # Basic CRUD Operations
    # -------------------------------------------------------------------------

    @staticmethod
    async def get_participant_ids() -> List[UUID]:
        """Get a list of all participant IDs."""
        return await Participant.all().values_list("id", flat=True)

    @staticmethod
    async def get_participant(participant_id: UUID) -> Optional[ParticipantPydantic]:
        """
        Get a participant by ID.

        Args:
            participant_id: The UUID of the participant to retrieve

        Returns:
            The participant data or None if not found
        """
        participant = await Participant.get_or_none(id=participant_id)
        if participant:
            return await ParticipantPydantic.from_orm(participant)
        return None

    @staticmethod
    async def create_participant(participant: ParticipantCreate) -> ParticipantPydantic:
        """
        Create a new participant.

        Args:
            participant: The participant data to create

        Returns:
            The created participant data
        """

        try:
            participant_obj = await Participant.create(**participant.model_dump())
            return await ParticipantPydantic.from_orm(participant_obj)
        except IntegrityError as e:
            logger.error("Unique constraint violation while creating participant.")
            raise ValueError("Participant with this email already exists.") from e

    @staticmethod
    async def update_participant(
        participant_id: UUID, participant: ParticipantUpdate
    ) -> Optional[ParticipantPydantic]:
        """
        Update an existing participant.

        Args:
            participant_id: The UUID of the participant to update
            participant: The updated participant data

        Returns:
            The updated participant data
        """
        await Participant.filter(id=participant_id).update(**participant.model_dump())
        updated_participant = await Participant.get_or_none(id=participant_id)
        return await ParticipantPydantic.from_orm(updated_participant)

    @staticmethod
    async def delete_participant(
        participant_id: Optional[UUID] = None,
        participant: Optional[Participant] = None,
    ) -> bool | None:
        """
        Mark a participant as deleted (soft delete).

        Args:
            participant_id: The UUID of the participant to delete (optional)
            participant: An already-fetched participant object (optional)

        Returns:
            True if a participant was found and marked as deleted, False otherwise.
        """
        participant = (
            await Participant.filter(
                id=participant_id,
                status__not=ParticipantStatus.DELETED,
            ).get_or_none()
            if not participant
            else participant
        )
        if not participant:
            return False
        participant.status = ParticipantStatus.DELETED
        await participant.save()
        logger.info(f"Changed status of participant: {participant_id} to deleted")

    # -------------------------------------------------------------------------
    # Listing and Pagination Operations
    # -------------------------------------------------------------------------

    @staticmethod
    async def get_participants(page: int, per_page: int) -> ParticipantsList:
        """
        Get a paginated list of participants with basic information.

        Args:
            page: The page number (1-based)
            per_page: The number of items per page

        Returns:
            A list of participants
        """
        participants = (
            await Participant.all()
            .prefetch_related("activities")
            .offset((page - 1) * per_page)
            .limit(per_page)
        )

        participants_list = []
        for participant in participants:
            participant_processed = await ParticipantPydantic.from_orm(participant)
            participants_list.append(participant_processed)

        return ParticipantsList(participants=participants_list)

    @staticmethod
    async def get_paginated_participants(
        *,
        page: int,
        per_page: int,
        filters: Optional[FilterInput] = None,
        sort: Optional[SortInput] = None,
    ) -> GetParticipantsOutput:
        """
        Fetch a paginated list of participants with optional filtering and sorting.

        Args:
            page: The current page number to fetch (1-based)
            per_page: The number of participants per page
            filters: Filtering options based on participant fields
            sort: Sorting options for participant fields

        Returns:
            An object containing the list of participants and total number of pages

        Raises:
            ValueError: If invalid sorting or filtering options are provided
        """
        # Build the base query with all necessary prefetches
        query = await ParticipantRepository._build_participant_query(filters)

        # Get total count for pagination
        total_participants = await query.count()
        total_pages = math.ceil(total_participants / per_page)

        # Apply sorting
        sort_by = ParticipantRepository._get_sort_field(sort)

        # Execute the query with pagination and sorting
        participants = (
            await query.offset((page - 1) * per_page).limit(per_page).order_by(sort_by)
        )

        # Process the results
        participants_output = await ParticipantRepository._process_participant_results(
            participants
        )

        return GetParticipantsOutput(
            participants=participants_output, total_pages=total_pages
        )

    @staticmethod
    async def get_participant_classes_progress(
        participant_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> list[ParticipantClassProgress]:
        """
        Fetch the class progress for a participant within a given date range.

        Args:
            participant_id: The UUID of the participant
            start_date: If provided,
                only include bookings where the live session's meeting_start_time is on or after this datetime.
            end_date: If provided,
                only include bookings where the live session's meeting_start_time is before this datetime.

        Returns:
            A list of ParticipantClassProgress objects
        """
        query = Booking.filter(
            participant_id=participant_id,
        )

        if start_date:
            query = query.filter(live_session__meeting_start_time__gte=start_date)
        if end_date:
            query = query.filter(live_session__meeting_start_time__lt=end_date)

        query = query.prefetch_related(
            "live_session__webinar__host",
            "participant",
        )

        bookings = await query.all().order_by("-live_session__meeting_start_time")

        return [
            ParticipantClassProgress(
                live_session=RawLiveSession.model_validate(booking.live_session),
                participant=RawParticipant.model_validate(booking.participant),
                booking=RawBooking.model_validate(booking),
                host=RawAuthorized.model_validate(booking.live_session.webinar.host),
                webinar=RawWebinar.model_validate(booking.live_session.webinar),
            )
            for booking in bookings
        ]

    # -------------------------------------------------------------------------
    # Specialized Operations
    # -------------------------------------------------------------------------

    @staticmethod
    async def disable_participant(
        email: Optional[str] = None,
        participant: Optional[Participant] = None,
        cohort: Cohort = None,
        disenrolledReason: Optional[str] = "No provided reason",
        disenrollmentDate: Optional[str] = None,
    ) -> bool | None:
        """
        Disable a participant and handle all related cleanup operations.

        Args:
            email: The email of the participant to disable
            cohort: The cohort the participant belongs to (optional)
            disenrolledReason: The reason for disenrollment
            disenrollmentDate: The date of disenrollment

        Returns:
            True if successful, None if participant not found
        """
        # Find the participant
        participant = (
            await ParticipantRepository._find_participant_by_email(email)
            if not participant
            else participant
        )
        email = email if email else participant.email
        if not participant:
            logger.info(f"Participant with email: {email} not found")
            return None

        try:
            async with in_transaction():
                # Mark as deleted
                await ParticipantRepository.delete_participant(participant=participant)

                # Update participant metadata
                await ParticipantRepository._update_disenrollment_metadata(
                    participant, disenrolledReason, disenrollmentDate
                )

                # Handle cohort removal
                cohort = await ParticipantRepository.handle_cohort_removal(
                    participant, cohort
                )

                # Delete bookings
                await ParticipantRepository._delete_participant_bookings(participant)

                # Handle Solera participant
                await SoleraParticipantRepository.disable_solera_participant(
                    participant.id
                )
            # These operations are performed outside the transaction as they involve external services
            # Handle Cognito user
            await ParticipantRepository._disable_cognito_user(email)

            # Send notification
            await ParticipantRepository._send_disenrollment_notification(participant.id)

            return True

        except Exception as e:
            logger.exception(f"Error disabling participant {email}: {str(e)}")
            raise

    @staticmethod
    async def get_participant_program(participant_id: UUID) -> Optional[Program]:
        """
        Get the program associated with a participant.

        Args:
            participant_id: The UUID of the participant

        Returns:
            The program or None if not found
        """
        participant = await Participant.filter(id=participant_id).get_or_none()
        if not participant:
            return None

        cohorts = await participant.cohort
        if not cohorts:
            return None

        return await cohorts[0].program

    # -------------------------------------------------------------------------
    # Authentication and Account Operations
    # -------------------------------------------------------------------------

    @staticmethod
    async def sign_up(
        participant: ParticipantService, info: Info
    ) -> Participant | None:
        """
        Sign up a new participant.

        Args:
            participant: The participant service instance
            info: GraphQL request info

        Returns:
            The created participant or None if failed
        """
        return await participant.sign_up(info)

    @staticmethod
    async def resend_confirmation_link(
        info: Info,
        email: str = None,
        token: str = None,
    ) -> bool:
        """
        Resend a confirmation link to a participant.

        Args:
            info: GraphQL request info
            email: The participant's email (optional)
            token: The activation token (optional)

        Returns:
            True if successful, False otherwise
        """
        if token:
            data = ParticipantService().decode_activation_token(token)
            if not data:
                return False
            email = data["email"].lower()

        return await ParticipantService().resend_activation_link(email=email, info=info)

    @staticmethod
    async def forgot_password(email: str) -> bool:
        """
        Initiate the forgot password flow for a participant.

        Args:
            email: The participant's email

        Returns:
            True if successful, False otherwise
        """
        return await ParticipantService().forgot_password(email=email)

    @staticmethod
    async def set_new_password(
        email: str, new_password: str, info: Info, correlation_id: str = "-"
    ) -> Participant | None:
        """
        Set a new password for a participant.

        Args:
            email: The participant's email
            new_password: The new password
            info: GraphQL request info
            correlation_id: Correlation ID for tracking

        Returns:
            The participant or None if failed
        """
        return await ParticipantService().set_new_password(
            info=info,
            email=email,
            new_password=new_password,
            correlation_id=correlation_id,
        )

    @staticmethod
    async def verify_change_password_token(token: str, info: Info) -> dict:
        """
        Verify a change password token.

        Args:
            token: The token to verify
            info: GraphQL request info

        Returns:
            Token verification result
        """
        return await ParticipantService().verify_change_password_token(
            token=token, info=info
        )

    @staticmethod
    async def update_status(
        participant_id: UUID, status: str, info: Info
    ) -> Participant | None:
        """
        Update a participant's status.

        Args:
            participant_id: The UUID of the participant
            status: The new status
            info: GraphQL request info

        Returns:
            The updated participant or None if failed
        """
        return await ParticipantService(
            participant_id=participant_id, status=status
        ).update_status(info)

    @staticmethod
    async def update_email(participant_id: UUID, email: str) -> bool:
        """
        Update a participant's email.

        Args:
            participant_id: The UUID of the participant
            email: The new email

        Returns:
            True if successful, False otherwise
        """
        return await ParticipantService(
            participant_id=participant_id, email=email
        ).update_email()

    # -------------------------------------------------------------------------
    # Helper Methods
    # -------------------------------------------------------------------------

    @staticmethod
    async def _build_participant_query(
        filters: Optional[FilterInput] = None,
    ) -> QuerySet:
        """Build a base query for participants with all necessary prefetches."""
        query = Participant.all_participants.all()

        # Ensure we prefetch all necessary relations
        prefetch_list = [
            # Cohort relations
            "cohorts",  # This is the CohortMembers relation
            "cohorts__cohort",  # This is the actual Cohort
            "cohorts__cohort__program_modules",
            "cohorts__cohort__program_modules__program_module",
            "cohorts__cohort__program",
            "cohorts__cohort__created_by",
            # Other relations
            "activities",
            "participant_meta",
            "solera_participant",
        ]
        query = query.prefetch_related(*prefetch_list)

        if filters:
            query = process_filters(query, filters)

        return query.distinct()

    @staticmethod
    def _get_sort_field(sort: Optional[SortInput] = None) -> str:
        """Determine the sort field and direction."""
        sort_by: str = "first_name"  # Default sort by first name

        if sort and sort.field:
            # Map FieldEnum values to corresponding model relations
            sort_by = SORT_KEY_MAPPING[sort.field]

            if sort.order == OrderEnum.desc:
                sort_by = f"-{sort_by}"

        return sort_by

    @staticmethod
    async def _process_participant_results(participants) -> list[ParticipantInfo]:
        """Process participant query results into ParticipantInfo objects."""
        participants_output: list[ParticipantInfo] = []

        for participant in participants:
            # Process activity data
            try:
                last_activity_date, enrolled_activity_date = (
                    process_participant_activity(participant)
                )
            except Exception as e:
                logger.warning(f"Error processing participant activities: {e}")
                last_activity_date, enrolled_activity_date = None, None

            # Process metadata
            try:
                phone, disenrolled_reason, disenrollment_date = (
                    process_participant_meta(participant)
                )
            except Exception as e:
                logger.warning(f"Error processing participant metadata: {e}")
                phone, disenrolled_reason, disenrollment_date = None, None, None

            # Process cohort information
            try:
                cohort_info = process_participant_cohort(participant)
            except Exception as e:
                logger.warning(f"Error processing participant cohort: {e}")
                cohort_info = None

            # Create participant info object
            participant_info = ParticipantInfo(
                id=participant.id,
                created_at=participant.created_at,
                updated_at=participant.updated_at,
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
                phone=phone,
                group_id=participant.group_id,
                member_id=participant.member_id,
                status=participant.status,
                cognito_sub=participant.cognito_sub,
                medical_record=participant.medical_record,
                is_test=participant.is_test,
                last_reset=participant.last_reset,
                chat_identity=participant.chat_identity,
                last_activity_date=last_activity_date,
                date_enrolled=enrolled_activity_date,
                cohort=cohort_info,
                dissenrolled_reason=disenrolled_reason,
                disenrollment_date=disenrollment_date if disenrollment_date else None,
            )

            participants_output.append(participant_info)

        return participants_output

    @staticmethod
    async def _find_participant_by_email(email: str) -> Optional[Participant]:
        """Find a participant by email with necessary prefetches."""
        return (
            await Participant.filter(email=email)
            .prefetch_related("solera_participant", "participant_meta")
            .first()
        )

    @staticmethod
    async def _update_disenrollment_metadata(
        participant: Participant,
        disenrolled_reason: str,
        disenrollment_date: Optional[str],
    ) -> None:
        """Update participant metadata for disenrollment."""
        if not participant.participant_meta:
            logger.warning(f"Participant {participant.id} has no metadata")
            return

        # Get active Solera participant
        solera_participant = await SoleraParticipant.filter(
            participant_id=participant.id, status=ParticipantStatus.ACTIVE
        ).get_or_none()

        # Format disenrollment date
        formatted_date = (
            disenrollment_date
            if disenrollment_date
            else pendulum.now("UTC").to_date_string()
        )

        # Update metadata
        participant_metadata = participant.participant_meta[0].metadata
        participant_metadata.update(
            {
                "enrolled": False,
                "disenrolledReason": disenrolled_reason,
                "disenrollmentDate": formatted_date,
                "solera_program_id": solera_participant.solera_program_id
                if solera_participant
                else None,
            }
        )

        # Save changes
        participant.participant_meta[0].metadata = participant_metadata
        await participant.participant_meta[0].save()
        await participant.save()

    @staticmethod
    async def handle_cohort_removal(
        participant: Participant, cohort: Optional[Cohort]
    ) -> Optional[Cohort]:
        """Handle removing participant from cohort and chat."""
        if not cohort:
            cohort = await Cohort.filter(participants__id=participant.id).first()
            if not cohort:
                logger.info("Participant is not part of any cohort")
                return None

        # Remove from cohort
        await CohortMembersRepository.delete_cohort_member(
            cohort_id=cohort.id, participant_id=participant.id
        )

        # Remove from chat
        removed_from_conversation = await remove_participant_from_chat(
            group_unique_name=cohort.unique_name, participant_id=participant.id
        )
        logger.info(f"Result from chat delete: {removed_from_conversation}")

        return cohort

    @staticmethod
    async def _delete_participant_bookings(participant: Participant) -> None:
        """Delete all bookings associated with a participant."""
        await Booking.filter(participant_id=participant.id).delete()

    @staticmethod
    async def _disable_cognito_user(email: str) -> None:
        """Disable a user in Cognito."""
        participant_service = ParticipantService()
        cognito_user = participant_service.invoke_cognito(
            admin_get_user,
            email,
        )

        if cognito_user and cognito_user["UserStatus"] != "DISABLED":
            user_disabled = participant_service.invoke_cognito(
                admin_disable_user, email
            )
            logger.info(f"Disabled user in cognito: {user_disabled}")

    @staticmethod
    async def _send_disenrollment_notification(participant_id: UUID) -> None:
        """Send a disenrollment notification via SQS."""
        notification = SQSNotification(
            type=NotificationType.SQS,
            email_event=EmailNotificationEvent.DISENROLL_PARTICIPANT,
            data={
                "participant_id": participant_id,
            },
        )

        try:
            send_to_sqs(
                queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                message_body=notification.model_dump_json(),
            )
        except Exception as e:
            logger.error(
                f"Unable to send disenroll notification for: {participant_id}, error: {e}"
            )


class SoleraParticipantRepository:
    @staticmethod
    async def get_solera_participants(
        page: int, per_page: int
    ) -> SoleraParticipantsList:
        solera_participants = (
            await SoleraParticipant.all().offset((page - 1) * per_page).limit(per_page)
        )

        processed_solera_participants = []
        if solera_participants:
            for sp in solera_participants:
                sp_processed = await SoleraParticipantPydantic.from_orm(sp)
                processed_solera_participants.append(sp_processed)
        return SoleraParticipantsList(participants=processed_solera_participants)

    @staticmethod
    async def create_solera_participant(
        sp: SoleraParticipantCreate,
    ) -> SoleraParticipantPydantic:
        sp_obj = await SoleraParticipant.create(**sp.model_dump())
        return await SoleraParticipantPydantic.from_orm(sp_obj)

    @staticmethod
    async def get_solera_participant(
        participant_id: UUID,
    ) -> Optional[SoleraParticipantPydantic]:
        sp = await SoleraParticipant.get_or_none(participant_id=participant_id)
        if sp:
            return await SoleraParticipantPydantic.from_orm(sp)
        return None

    @staticmethod
    async def update_solera_participant(
        participant_id: UUID, sp: SoleraParticipantUpdate
    ) -> Optional[SoleraParticipantPydantic]:
        await SoleraParticipant.filter(participant_id=participant_id).update(
            **sp.model_dump()
        )
        updated_sp = await SoleraParticipant.get(participant_id=participant_id)
        return await SoleraParticipantPydantic.from_orm(updated_sp)

    @staticmethod
    async def delete_solera_participant(participant_id: UUID) -> None:
        await SoleraParticipant.filter(participant_id=participant_id).delete()

    @staticmethod
    async def disable_solera_participant(participant_id: UUID) -> None:
        """
        Disable a Solera participant by ID.

        Args:
            participant_id: The UUID of the Solera participant to disable
        """
        await SoleraParticipant.filter(participant_id=participant_id).update(
            status=ParticipantStatus.DELETED
        )
        logger.info(
            f"Changed status of Solera participant: {participant_id} to disabled"
        )


class HeadsUpParticipantRepository:
    @staticmethod
    async def get_heads_up_participants(
        page: int, per_page: int
    ) -> HeadsUpParticipantsList:
        heads_up_participants = (
            await HeadsUpParticipant.all().offset((page - 1) * per_page).limit(per_page)
        )
        processed_hup_list = []
        if heads_up_participants:
            for hup in heads_up_participants:
                processed_hup = await HeadsUpParticipantPydantic.from_orm(hup)
                processed_hup_list.append(processed_hup)
        return HeadsUpParticipantsList(heads_up_participants=processed_hup_list)

    @staticmethod
    async def create_heads_up_participant(
        hup: HeadsUpParticipantCreate,
    ) -> HeadsUpParticipantPydantic:
        hup_obj = await HeadsUpParticipant.create(**hup.model_dump())
        return await HeadsUpParticipantPydantic.from_orm(hup_obj)

    @staticmethod
    async def get_heads_up_participant(
        participant_id: UUID,
    ) -> Optional[HeadsUpParticipantPydantic]:
        hup = await HeadsUpParticipant.get_or_none(participant_id=participant_id)
        if hup:
            return await HeadsUpParticipantPydantic.from_orm(hup)
        return None

    @staticmethod
    async def update_heads_up_participant(
        participant_id: UUID, hup: HeadsUpParticipantUpdate
    ) -> Optional[HeadsUpParticipantPydantic]:
        await HeadsUpParticipant.filter(participant_id=participant_id).update(
            **hup.model_dump()
        )
        updated_hup = await HeadsUpParticipant.get(participant_id=participant_id)
        return await HeadsUpParticipantPydantic.from_orm(updated_hup)

    @staticmethod
    async def delete_heads_up_participant(participant_id: UUID) -> None:
        await HeadsUpParticipant.filter(participant_id=participant_id).delete()


class ParticipantMetaRepository:
    @staticmethod
    async def get_participant_metas(page: int, per_page: int) -> MetaParticipantsList:
        participant_metas = (
            await ParticipantMeta.all().offset((page - 1) * per_page).limit(per_page)
        )
        processed_pm_list = []
        if participant_metas:
            for pm in participant_metas:
                processed_pm = await ParticipantMetaPydantic.from_orm(pm)
                processed_pm_list.append(processed_pm)
        return MetaParticipantsList(participant_metas=processed_pm_list)

    @staticmethod
    async def create_participant_meta(
        pm: ParticipantMetaCreate,
    ) -> ParticipantMetaPydantic:
        pm_obj = await ParticipantMeta.create(**pm.model_dump())
        return await ParticipantMetaPydantic.from_orm(pm_obj)

    @staticmethod
    async def get_participant_meta(
        participant_id: UUID,
    ) -> Optional[ParticipantMetaPydantic]:
        pm = await ParticipantMeta.get_or_none(participant_id=participant_id)
        if pm:
            return await ParticipantMetaPydantic.from_orm(pm)
        return None

    @staticmethod
    async def update_participant_meta(
        pm: ParticipantMetaUpdate,
    ) -> Optional[ParticipantMetaPydantic]:
        await ParticipantMeta.filter(id=pm.id).update(metadata=pm.metadata)
        updated_pm = await ParticipantMeta.get(id=pm.id)
        return await ParticipantMetaPydantic.from_orm(updated_pm)

    @staticmethod
    async def update_participant_address(
        participant_id: UUID, address: ParticipantAddress
    ) -> Optional[ParticipantMetaPydantic]:
        pm = await ParticipantMeta.get_or_none(participant_id=participant_id)
        if not pm:
            return None

        if not pm.metadata:
            pm.metadata = {}

        if "address" not in pm.metadata or not isinstance(pm.metadata["address"], dict):
            pm.metadata["address"] = {}

        address_dict = address.model_dump(exclude_none=True)
        pm.metadata["address"].update(address_dict)

        await pm.save()
        return await ParticipantMetaPydantic.from_orm(pm)

    @staticmethod
    async def delete_participant_meta(participant_id: UUID) -> None:
        await ParticipantMeta.filter(participant_id=participant_id).delete()


class AuthorizedRepository:
    @staticmethod
    async def list_authorized() -> List[RawAuthorized]:
        all_authorized = await Authorized.all()

        return [
            RawAuthorized.model_validate(authorized) for authorized in all_authorized
        ]

    @staticmethod
    async def list_active_authorized() -> List[RawAuthorized]:
        active_authorized = await Authorized.filter(
            status=ParticipantStatus.ACTIVE,
            is_test=False,
        ).all()

        return [
            RawAuthorized.model_validate(authorized) for authorized in active_authorized
        ]

    @staticmethod
    async def get_alternative_hosts() -> List[RawAuthorized]:
        alternative_hosts = await Authorized.filter(alternative_host=True)

        return [
            RawAuthorized.model_validate(authorized) for authorized in alternative_hosts
        ]

    @staticmethod
    async def get_supports() -> List[RawAuthorized]:
        supports = await Authorized.filter(support_in_chat=True)

        return [RawAuthorized.model_validate(support) for support in supports]

    @staticmethod
    async def get_class_admins() -> List[RawAuthorized]:
        class_admins = await Authorized.filter(classes_admin=True)

        return [RawAuthorized.model_validate(admin) for admin in class_admins]

    @staticmethod
    async def get_content_library_admins() -> List[RawAuthorized]:
        content_admins = await Authorized.filter(content_admin=True)

        return [RawAuthorized.model_validate(admin) for admin in content_admins]

    @staticmethod
    async def get_full_admins() -> List[RawAuthorized]:
        admins = await Authorized.filter(
            role=AutorizedRole.ADMIN,
            support_in_chat=True,
            classes_admin=True,
            content_admin=True,
        )

        return [RawAuthorized.model_validate(admin) for admin in admins]

    @staticmethod
    async def get_authorized(authorized_id: UUID) -> Optional[RawAuthorized]:
        authorized = await Authorized.get_or_none(id=authorized_id)

        if not authorized:
            return None

        return RawAuthorized.model_validate(authorized)

    @staticmethod
    async def get_by_schedule_id(schedule_id: int) -> Optional[RawAuthorized]:
        authorized = await Authorized.get_or_none(supersaas_schedule_id=schedule_id)

        if not authorized:
            return None

        return RawAuthorized.model_validate(authorized)

    @staticmethod
    async def create_authorized(
        authorized: AuthorizedCreate, password: str
    ) -> RawAuthorized:
        """Create an authorized user in Cognito and save the user in the database."""
        exist = await Authorized.filter(email=authorized.email).exists()
        if exist:
            raise ValueError(
                f"Authorized with email: {authorized.email}, already exists"
            )

        response = admin_create_provider(
            email=authorized.email,
            is_participant_admin=True,
        )
        logger.info(f"Create provider response: {response}")

        cognito_sub = response["User"]["Username"]

        resp = admin_set_provider_password(authorized.email, password, True)
        logger.info(f"Set provider password response: {resp}")

        authorized_user = Authorized(
            id=cognito_sub,
            email=authorized.email,
            first_name=authorized.first_name,
            last_name=authorized.last_name,
            cognito_sub=cognito_sub,
            status=ParticipantStatus.ACTIVE,
            role=authorized.role,
        )
        await authorized_user.save()
        return RawAuthorized.model_validate(authorized_user)

    @staticmethod
    async def update_authorized(
        authorized_id: UUID, authorized: AuthorizedUpdate
    ) -> AuthorizedPydantic:
        await Authorized.filter(id=authorized_id).update(
            **authorized.model_dump(exclude_none=True, exclude={"id"})
        )
        updated_authorized = await Authorized.get(id=authorized_id)
        return await AuthorizedPydantic.from_orm(updated_authorized)

    @staticmethod
    async def delete_authorized(authorized_id: UUID) -> None:
        user = await Authorized.filter(id=authorized_id).get_or_none()

        if user:
            cohorts = await user.cohorts
            if len(cohorts) > 0:
                raise ValueError(
                    "Cannot delete authorized user with associated cohorts"
                )
            await user.delete()
