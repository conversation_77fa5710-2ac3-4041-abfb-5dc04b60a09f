from datetime import datetime
from enum import StrEnum
from typing import Optional, List, Dict, Any

import strawberry
from pydantic import BaseModel


@strawberry.enum
class DeviceStatusEnum(StrEnum):
    NOT_CONNECTED = "not_connected"
    CONNECTED = "connected"
    RECONNECT = "reconnect"


@strawberry.enum
class DeviceTypeEnum(StrEnum):
    WITHINGS = "withings"
    FITBIT = "fitbit"
    DEXCOM = "dexcom"
    OURARING = "ouraring"
    TRANSTEK = "transtek"


@strawberry.enum
class DeviceModelEnum(StrEnum):
    SCALE = "scale"
    BPM = "bpm"


@strawberry.type
class DeviceStatus:
    status: DeviceStatusEnum
    device: DeviceTypeEnum
    device_id: Optional[str] = None
    model: Optional[DeviceModelEnum] = None
    battery: Optional[int] = None
    signal: Optional[int] = None


@strawberry.type
class Subscription:
    expires_in: int


@strawberry.type
class DetailedConnectionStatus(DeviceStatus):
    id: Optional[str] = None
    token: Optional[str] = None
    healthy: Optional[bool] = None
    account_id: Optional[str] = None
    subscription: Optional[Subscription] = None
    auth_url: Optional[str] = None
    battery: Optional[int] = None
    signal: Optional[int] = None


class Measure(BaseModel):
    value: float
    unit: str
    created_at: datetime


class Device(BaseModel):
    id: str
    device_type: str
    last_synced_at: datetime


class LatestData(BaseModel):
    last_ciba_sync: Optional[datetime] = None
    last_device_sync: Optional[datetime] = None
    measures: List[Measure] = []
    devices: List[Device] = []


class SyncProcessing(BaseModel):
    success: bool


class TranstekTrackingData(BaseModel):
    tracking_number: str
    carrier: str


class TranstekDeviceInfo(BaseModel):
    """Simplified response model for Transtek device."""

    id: str
    device_id: str
    imei: str
    model: str
    device_type: str
    status: str
    created_at: str
    updated_at: str

    # Optional fields
    tracking_number: Optional[str] = None
    carrier: Optional[str] = None
    tracking_url: Optional[str] = None
    timezone: Optional[str] = None
    last_status_report: Optional[Dict[str, Any]] = None
    member_id: Optional[str] = None

    class Config:
        from_attributes = True
