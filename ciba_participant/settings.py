from enum import Enum
from typing import Any
from urllib.parse import quote_plus
from pydantic_settings import BaseSettings
from ciba_participant.common.aws_handler import load_specific_parameters


class ENV(str, Enum):
    DEV = "dev"
    PROD = "prod"
    LOCAL = "local"
    STG = "stg"
    TEST = "test"


class Settings(BaseSettings):
    PROJECT_NAME: str = "CibaHealth participant API"
    DEBUG: int = 0
    VERSION: str = "1.3.0"
    ENV: str = ENV.LOCAL  # Use ENV enum for type
    DEV_EMAIL: str = "<EMAIL>"

    POSTGRES_USER: str = "participant"
    POSTGRES_PASSWORD: str = "oqMgucgt{_#SEYRnC$*)!#bz"
    POSTGRES_DB: str = "participant_v2"
    POSTGRES_HOST: str = "participant-prod-api.cb9f0wftv7af.us-east-2.rds.amazonaws.com"
    POSTGRES_PORT: int = 5432
    SOLERA_CLIENT_ID: str = ""
    SOLERA_CLIENT_SECRET: str = ""
    SOLERA_AUTH_URL: str = "http://test"
    SOLERA_API_URL: str = "http://test"
    AWS_BUCKET_NAME: str = ""
    CONTENT_LIBRARY_BUCKET_NAME: str = ""
    CONTENT_LIBRARY_KEY_ID: str = ""
    CONTENT_LIBRARY_SIGN_KEY: str = ""
    CONTENT_LIBRARY_DISTRIBUTION: str = ""
    AWS_REGION: str = "us-east-2"
    COGNITO_AWS_REGION: str = "us-east-2"
    COGNITO_APP_CLIENT_ID: str = ""
    COGNITO_USER_POOL_ID: str = ""
    COGNITO_SERVER_CLIENT_ID: str = ""
    COGNITO_SERVER_CLIENT_SECRET: str = ""
    COGNITO_KMS_KEY_ARN: str = ""
    COGNITO_USER_GROUP: str = "participant"

    PROVIDER_APP_CLIENT_ID: str = ""
    PROVIDER_USER_POOL_ID: str = ""

    SCHEDULE_MANAGER_API_ENDPOINT: str = (
        "https://zsishcd5qb.execute-api.us-east-2.amazonaws.com/schedule-manager"
    )

    REDIS_PORT: int = 6379
    REDIS_HOST: str = "localhost"
    CHAT_API_HOST: str = ""
    CHAT_API_KEY: str = ""

    SECRET_KEY: str = ""

    PARTICIPANT_EMAIL_SQS_URL: str = ""
    SENDGRID_API_KEY: str = ""
    SENDGRID_REQUEST_TIMEOUT: int = 60
    DO_NOT_REPLY: tuple = ("<EMAIL>", "Cibahealth")

    CIBA_API_HOST: str = ""
    CIBA_API_KEY: str = ""
    IS_NEW_ENV: int = 0
    IS_LAMBDA: int = 0

    UI_HOST: str = ""
    ADMIN_UI_HOST: str = ""

    PROVIDERS_COGNITO_APP_CLIENT_ID: str = ""
    PROVIDERS_COGNITO_USER_POOL_ID: str = ""
    ADMIN_SENTRY_DSN: str = ""
    SLACK_SNS_TOPIC_ARN: str = (
        "arn:aws:sns:us-east-2:572827854243:slack-notification-topic"
    )
    KINESIS_STREAM_NAME: str = "ciba-engagement-data-stream"

    RPM_API_URL: str = "http://rpm_registration_api:8000/"
    RPM_API_KEY: str = "123"

    MINTVAULT_API_URL: str = "http://mintvault_api:8000/"
    MINTVAULT_API_KEY: str = ""

    @property
    def default_db_url(self) -> str:
        """Construct default database URL."""
        return (
            f"postgres://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )


class SettingsManager:
    """Singleton class to manage settings without global variables."""

    def __init__(self):
        self._settings = None
        self._parameters_pulled = False

    def get_settings(self, **kwargs: Any) -> Settings:
        """Retrieve cached settings or initialize new settings."""
        settings_data = kwargs.copy()
        temp_settings = Settings(**settings_data)

        if not temp_settings.IS_LAMBDA:
            return temp_settings

        if self._settings is not None:
            if kwargs:
                updated_settings_data = vars(self._settings).copy()
                updated_settings_data.update(kwargs)
                return Settings(**updated_settings_data)

            return self._settings

        if temp_settings.ENV in [ENV.LOCAL, ENV.TEST]:
            return temp_settings

        if not self._parameters_pulled:
            parameter_names = [
                "POSTGRES_DB",
                "POSTGRES_USER",
                "POSTGRES_PASSWORD",
                "POSTGRES_HOST",
                "POSTGRES_PORT",
                "COGNITO_APP_CLIENT_ID",
                "COGNITO_USER_POOL_ID",
                "COGNITO_SERVER_CLIENT_ID",
                "COGNITO_SERVER_CLIENT_SECRET",
                "COGNITO_KMS_KEY_ARN",
                "SCHEDULE_MANAGER_API_ENDPOINT",
                "CHAT_API_HOST",
                "CHAT_API_KEY",
                "PARTICIPANT_EMAIL_SQS_URL",
                "CIBA_API_HOST",
                "CIBA_API_KEY",
                "SOLERA_CLIENT_ID",
                "SOLERA_CLIENT_SECRET",
                "PROVIDERS_COGNITO_APP_CLIENT_ID",
                "PROVIDERS_COGNITO_USER_POOL_ID",
                "PROVIDER_APP_CLIENT_ID",
                "PROVIDER_USER_POOL_ID",
                "ADMIN_SENTRY_DSN",
                "AWS_BUCKET_NAME",
                "CONTENT_LIBRARY_BUCKET_NAME",
                "SOLERA_AUTH_URL",
                "SOLERA_API_URL",
                "RPM_API_URL",
                "RPM_API_KEY",
                "SLACK_SNS_TOPIC_ARN",
            ]

            settings_data = load_specific_parameters(
                parameter_names=parameter_names,
                env=temp_settings.ENV,
                service_prefix="participant",
                is_new_env=bool(temp_settings.IS_NEW_ENV),
            )
            self._parameters_pulled = True

        self._settings = Settings(**settings_data)
        return self._settings

    def reset(self):
        """Reset cached settings (for testing purposes)."""
        self._settings = None
        self._parameters_pulled = False


# Create a singleton instance
settings_manager = SettingsManager()


# Use this function to get settings
def get_settings(**kwargs: Any) -> Settings:
    return settings_manager.get_settings(**kwargs)


# Use this function to reset settings in tests
def reset_cached_settings():
    settings_manager.reset()
